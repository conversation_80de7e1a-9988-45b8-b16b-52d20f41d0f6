# تطبيق الاستراتيجية الذكية الهجينة

## نظرة عامة على الاستراتيجية

الاستراتيجية المقترحة هي نظام **سكالبينغ ذكي متعدد الطبقات** يجمع بين:
- **التحليل الفني المتقدم** (Technical Analysis)
- **التحليل السلوكي** (Behavioral Analysis) 
- **الذكاء الاصطناعي** (AI/ML Analysis)

## معمارية الاستراتيجية

```
┌─────────────────────────────────────────────────────────────┐
│                    Market Data Input                        │
│  (Real-time Candles + Historical Data + Volume)            │
└─────────────────┬───────────────────────────────────────────┘
                  │
┌─────────────────▼───────────────────────────────────────────┐
│                Layer 1: Technical Analysis                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │
│  │ EMA Cross   │ │ RSI Analysis│ │ Bollinger Bands        │ │
│  │ (5,10)      │ │ (5 period)  │ │ (20,2)                 │ │
│  └─────────────┘ └─────────────┘ └─────────────────────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │
│  │ Momentum    │ │ ATR Filter  │ │ Heiken Ashi            │ │
│  │ (10)        │ │ (5)         │ │ Smoothing              │ │
│  └─────────────┘ └─────────────┘ └─────────────────────────┘ │
│                    ↓ Technical Score (0-100)                │
└─────────────────┬───────────────────────────────────────────┘
                  │
┌─────────────────▼───────────────────────────────────────────┐
│                Layer 2: Behavioral Analysis                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │
│  │ Candle      │ │ Price Action│ │ Volume Confirmation     │ │
│  │ Patterns    │ │ Analysis    │ │ (if available)          │ │
│  └─────────────┘ └─────────────┘ └─────────────────────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │
│  │ Momentum    │ │ Reversal    │ │ Trend Continuation      │ │
│  │ Candles     │ │ Patterns    │ │ Patterns                │ │
│  └─────────────┘ └─────────────┘ └─────────────────────────┘ │
│                   ↓ Behavioral Score (0-100)                │
└─────────────────┬───────────────────────────────────────────┘
                  │
┌─────────────────▼───────────────────────────────────────────┐
│                Layer 3: AI/ML Analysis                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │
│  │ LSTM        │ │ XGBoost     │ │ Random Forest           │ │
│  │ Predictor   │ │ Classifier  │ │ Ensemble                │ │
│  └─────────────┘ └─────────────┘ └─────────────────────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │
│  │ Market      │ │ Confidence  │ │ Risk Assessment         │ │
│  │ Regime      │ │ Calculator  │ │ Module                  │ │
│  │ Detector    │ │             │ │                         │ │
│  └─────────────┘ └─────────────┘ └─────────────────────────┘ │
│                     ↓ AI Score (0-100)                      │
└─────────────────┬───────────────────────────────────────────┘
                  │
┌─────────────────▼───────────────────────────────────────────┐
│                Decision Fusion Engine                       │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ Weighted Scoring System:                                │ │
│  │ • Technical Score × 0.4                                │ │
│  │ • Behavioral Score × 0.3                               │ │
│  │ • AI Score × 0.3                                       │ │
│  │ Final Score = Σ(weighted scores)                       │ │
│  └─────────────────────────────────────────────────────────┘ │
│                     ↓ Final Decision                        │
└─────────────────┬───────────────────────────────────────────┘
                  │
┌─────────────────▼───────────────────────────────────────────┐
│                Signal Generation                            │
│  IF Final Score ≥ 80 AND All Filters Pass:                │
│  → Generate CALL/PUT Signal with Confidence Level          │
└─────────────────────────────────────────────────────────────┘
```

## تطبيق الطبقات

### 1. Technical Analysis Layer

```python
class TechnicalAnalysisLayer:
    def __init__(self):
        self.indicators = TechnicalIndicators()
        self.weights = {
            'ema_cross': 0.25,
            'rsi': 0.20,
            'bollinger': 0.20,
            'momentum': 0.15,
            'atr_filter': 0.10,
            'heiken_ashi': 0.10
        }
    
    async def analyze(self, candles_data: List[Dict]) -> Dict:
        """
        تحليل فني شامل للبيانات
        """
        results = {}
        
        # 1. EMA Crossover Analysis
        ema5 = self.indicators.calculate_ema(candles_data, 5)
        ema10 = self.indicators.calculate_ema(candles_data, 10)
        results['ema_cross'] = self._analyze_ema_cross(ema5, ema10)
        
        # 2. RSI Analysis
        rsi = self.indicators.calculate_rsi(candles_data, 5)
        results['rsi'] = self._analyze_rsi(rsi)
        
        # 3. Bollinger Bands Analysis
        bb = self.indicators.calculate_bollinger_bands(candles_data, 20, 2)
        results['bollinger'] = self._analyze_bollinger(candles_data, bb)
        
        # 4. Momentum Analysis
        momentum = self.indicators.calculate_momentum(candles_data, 10)
        results['momentum'] = self._analyze_momentum(momentum)
        
        # 5. ATR Filter
        atr = self.indicators.calculate_atr(candles_data, 5)
        results['atr_filter'] = self._analyze_atr_filter(atr)
        
        # 6. Heiken Ashi Analysis
        ha_candles = self._calculate_heiken_ashi(candles_data)
        results['heiken_ashi'] = self._analyze_heiken_ashi(ha_candles)
        
        # حساب النتيجة النهائية
        technical_score = self._calculate_weighted_score(results)
        
        return {
            'score': technical_score,
            'direction': self._determine_direction(results),
            'strength': self._calculate_strength(results),
            'details': results
        }
    
    def _analyze_ema_cross(self, ema5: List[float], ema10: List[float]) -> Dict:
        """تحليل تقاطع المتوسطات المتحركة"""
        if len(ema5) < 2 or len(ema10) < 2:
            return {'score': 0, 'signal': 'neutral'}
        
        current_diff = ema5[-1] - ema10[-1]
        previous_diff = ema5[-2] - ema10[-2]
        
        if current_diff > 0 and previous_diff <= 0:
            # تقاطع صعودي
            return {'score': 100, 'signal': 'bullish', 'type': 'crossover_up'}
        elif current_diff < 0 and previous_diff >= 0:
            # تقاطع هبوطي
            return {'score': 100, 'signal': 'bearish', 'type': 'crossover_down'}
        elif current_diff > 0:
            # اتجاه صعودي مستمر
            strength = min(abs(current_diff) * 1000, 100)
            return {'score': strength, 'signal': 'bullish', 'type': 'trending_up'}
        else:
            # اتجاه هبوطي مستمر
            strength = min(abs(current_diff) * 1000, 100)
            return {'score': strength, 'signal': 'bearish', 'type': 'trending_down'}
    
    def _analyze_rsi(self, rsi: List[float]) -> Dict:
        """تحليل مؤشر القوة النسبية"""
        if not rsi:
            return {'score': 0, 'signal': 'neutral'}
        
        current_rsi = rsi[-1]
        
        if current_rsi < 30:
            # منطقة تشبع بيعي - إشارة شراء
            return {'score': 100 - current_rsi, 'signal': 'bullish', 'type': 'oversold'}
        elif current_rsi > 70:
            # منطقة تشبع شرائي - إشارة بيع
            return {'score': current_rsi - 50, 'signal': 'bearish', 'type': 'overbought'}
        elif 45 <= current_rsi <= 55:
            # منطقة محايدة
            return {'score': 20, 'signal': 'neutral', 'type': 'neutral_zone'}
        elif current_rsi > 50:
            # قوة صعودية
            strength = (current_rsi - 50) * 2
            return {'score': strength, 'signal': 'bullish', 'type': 'bullish_momentum'}
        else:
            # قوة هبوطية
            strength = (50 - current_rsi) * 2
            return {'score': strength, 'signal': 'bearish', 'type': 'bearish_momentum'}
```

### 2. Behavioral Analysis Layer

```python
class BehavioralAnalysisLayer:
    def __init__(self):
        self.pattern_weights = {
            'momentum_candles': 0.30,
            'reversal_patterns': 0.25,
            'continuation_patterns': 0.20,
            'volume_confirmation': 0.15,
            'price_action': 0.10
        }
    
    async def analyze(self, candles_data: List[Dict]) -> Dict:
        """
        تحليل سلوكي شامل للبيانات
        """
        results = {}
        
        # 1. تحليل شموع الزخم
        results['momentum_candles'] = self._analyze_momentum_candles(candles_data)
        
        # 2. تحليل أنماط الانعكاس
        results['reversal_patterns'] = self._analyze_reversal_patterns(candles_data)
        
        # 3. تحليل أنماط الاستمرار
        results['continuation_patterns'] = self._analyze_continuation_patterns(candles_data)
        
        # 4. تأكيد الحجم (إن توفر)
        results['volume_confirmation'] = self._analyze_volume_confirmation(candles_data)
        
        # 5. تحليل حركة السعر
        results['price_action'] = self._analyze_price_action(candles_data)
        
        # حساب النتيجة النهائية
        behavioral_score = self._calculate_weighted_score(results)
        
        return {
            'score': behavioral_score,
            'direction': self._determine_direction(results),
            'confidence': self._calculate_confidence(results),
            'details': results
        }
    
    def _analyze_momentum_candles(self, candles: List[Dict]) -> Dict:
        """تحليل شموع الزخم القوي"""
        if len(candles) < 3:
            return {'score': 0, 'signal': 'neutral'}
        
        last_candle = candles[-1]
        body_size = abs(last_candle['close'] - last_candle['open'])
        total_size = last_candle['high'] - last_candle['low']
        
        if total_size == 0:
            return {'score': 0, 'signal': 'neutral'}
        
        body_ratio = body_size / total_size
        
        # شمعة زخم قوي (جسم كبير، ظلال صغيرة)
        if body_ratio > 0.8:
            direction = 'bullish' if last_candle['close'] > last_candle['open'] else 'bearish'
            return {
                'score': 100,
                'signal': direction,
                'type': 'strong_momentum',
                'body_ratio': body_ratio
            }
        elif body_ratio > 0.6:
            direction = 'bullish' if last_candle['close'] > last_candle['open'] else 'bearish'
            return {
                'score': 70,
                'signal': direction,
                'type': 'moderate_momentum',
                'body_ratio': body_ratio
            }
        else:
            return {
                'score': 20,
                'signal': 'neutral',
                'type': 'weak_momentum',
                'body_ratio': body_ratio
            }
    
    def _analyze_reversal_patterns(self, candles: List[Dict]) -> Dict:
        """تحليل أنماط الانعكاس"""
        if len(candles) < 3:
            return {'score': 0, 'signal': 'neutral'}
        
        # فحص نمط Pin Bar
        pin_bar = self._detect_pin_bar(candles[-1])
        if pin_bar['detected']:
            return {
                'score': 90,
                'signal': pin_bar['direction'],
                'type': 'pin_bar',
                'details': pin_bar
            }
        
        # فحص نمط Doji
        doji = self._detect_doji(candles[-1])
        if doji['detected']:
            return {
                'score': 60,
                'signal': 'neutral',
                'type': 'doji',
                'details': doji
            }
        
        # فحص نمط Engulfing
        engulfing = self._detect_engulfing(candles[-2:])
        if engulfing['detected']:
            return {
                'score': 85,
                'signal': engulfing['direction'],
                'type': 'engulfing',
                'details': engulfing
            }
        
        return {'score': 0, 'signal': 'neutral', 'type': 'no_pattern'}
    
    def _detect_pin_bar(self, candle: Dict) -> Dict:
        """كشف نمط Pin Bar"""
        body_size = abs(candle['close'] - candle['open'])
        upper_shadow = candle['high'] - max(candle['open'], candle['close'])
        lower_shadow = min(candle['open'], candle['close']) - candle['low']
        total_size = candle['high'] - candle['low']
        
        if total_size == 0:
            return {'detected': False}
        
        # Pin Bar صعودي (ذيل سفلي طويل)
        if (lower_shadow > body_size * 2 and 
            lower_shadow > upper_shadow * 2 and
            lower_shadow / total_size > 0.6):
            return {
                'detected': True,
                'direction': 'bullish',
                'strength': lower_shadow / total_size
            }
        
        # Pin Bar هبوطي (ذيل علوي طويل)
        if (upper_shadow > body_size * 2 and 
            upper_shadow > lower_shadow * 2 and
            upper_shadow / total_size > 0.6):
            return {
                'detected': True,
                'direction': 'bearish',
                'strength': upper_shadow / total_size
            }
        
        return {'detected': False}
```

### 3. AI Analysis Layer

```python
class AIAnalysisLayer:
    def __init__(self):
        self.models = {
            'lstm_predictor': None,  # سيتم تحميله
            'xgboost_classifier': None,  # سيتم تحميله
            'random_forest': None  # سيتم تحميله
        }
        self.feature_extractor = FeatureExtractor()
        self.confidence_calculator = ConfidenceCalculator()
    
    async def predict(self, candles_data: List[Dict], 
                     technical_result: Dict, 
                     behavioral_result: Dict) -> Dict:
        """
        تحليل بالذكاء الاصطناعي
        """
        # استخراج الميزات
        features = self.feature_extractor.extract_features(
            candles_data, technical_result, behavioral_result
        )
        
        # التنبؤ بالاتجاه
        direction_prediction = await self._predict_direction(features)
        
        # حساب نسبة الثقة
        confidence = self.confidence_calculator.calculate(
            features, direction_prediction
        )
        
        # تحديد نظام السوق
        market_regime = self._detect_market_regime(candles_data)
        
        # تقييم المخاطر
        risk_assessment = self._assess_risk(features, market_regime)
        
        # حساب النتيجة النهائية
        ai_score = self._calculate_ai_score(
            direction_prediction, confidence, market_regime, risk_assessment
        )
        
        return {
            'score': ai_score,
            'direction': direction_prediction['direction'],
            'confidence': confidence,
            'market_regime': market_regime,
            'risk_level': risk_assessment['level'],
            'details': {
                'prediction': direction_prediction,
                'features': features,
                'risk': risk_assessment
            }
        }
    
    async def _predict_direction(self, features: np.ndarray) -> Dict:
        """التنبؤ بالاتجاه باستخدام النماذج المختلفة"""
        predictions = {}
        
        # LSTM Prediction
        if self.models['lstm_predictor']:
            lstm_pred = self.models['lstm_predictor'].predict(features)
            predictions['lstm'] = {
                'direction': 'bullish' if lstm_pred > 0.5 else 'bearish',
                'probability': float(lstm_pred)
            }
        
        # XGBoost Classification
        if self.models['xgboost_classifier']:
            xgb_pred = self.models['xgboost_classifier'].predict_proba(features)
            predictions['xgboost'] = {
                'direction': 'bullish' if xgb_pred[1] > 0.5 else 'bearish',
                'probability': float(xgb_pred[1])
            }
        
        # Random Forest Ensemble
        if self.models['random_forest']:
            rf_pred = self.models['random_forest'].predict_proba(features)
            predictions['random_forest'] = {
                'direction': 'bullish' if rf_pred[1] > 0.5 else 'bearish',
                'probability': float(rf_pred[1])
            }
        
        # دمج التنبؤات
        final_prediction = self._ensemble_predictions(predictions)
        
        return final_prediction
    
    def _ensemble_predictions(self, predictions: Dict) -> Dict:
        """دمج تنبؤات النماذج المختلفة"""
        if not predictions:
            return {'direction': 'neutral', 'probability': 0.5}
        
        bullish_votes = 0
        bearish_votes = 0
        total_probability = 0
        
        for model_name, pred in predictions.items():
            if pred['direction'] == 'bullish':
                bullish_votes += 1
                total_probability += pred['probability']
            else:
                bearish_votes += 1
                total_probability += (1 - pred['probability'])
        
        total_models = len(predictions)
        avg_probability = total_probability / total_models
        
        if bullish_votes > bearish_votes:
            return {
                'direction': 'bullish',
                'probability': avg_probability,
                'consensus': bullish_votes / total_models
            }
        elif bearish_votes > bullish_votes:
            return {
                'direction': 'bearish', 
                'probability': avg_probability,
                'consensus': bearish_votes / total_models
            }
        else:
            return {
                'direction': 'neutral',
                'probability': 0.5,
                'consensus': 0.5
            }
```

### 4. Decision Fusion Engine

```python
class DecisionFusionEngine:
    def __init__(self):
        self.layer_weights = {
            'technical': 0.4,
            'behavioral': 0.3,
            'ai': 0.3
        }
        self.minimum_score = 80  # الحد الأدنى للنتيجة
        self.minimum_confidence = 0.8  # الحد الأدنى للثقة
    
    async def make_decision(self, 
                          technical_result: Dict,
                          behavioral_result: Dict, 
                          ai_result: Dict) -> Dict:
        """
        اتخاذ القرار النهائي بدمج نتائج الطبقات الثلاث
        """
        # حساب النتيجة المرجحة
        weighted_score = (
            technical_result['score'] * self.layer_weights['technical'] +
            behavioral_result['score'] * self.layer_weights['behavioral'] +
            ai_result['score'] * self.layer_weights['ai']
        )
        
        # تحديد الاتجاه بالإجماع
        direction_consensus = self._determine_direction_consensus(
            technical_result, behavioral_result, ai_result
        )
        
        # حساب نسبة الثقة الإجمالية
        overall_confidence = self._calculate_overall_confidence(
            technical_result, behavioral_result, ai_result
        )
        
        # فحص الفلاتر الإضافية
        filters_passed = self._check_additional_filters(
            technical_result, behavioral_result, ai_result
        )
        
        # اتخاذ القرار النهائي
        should_trade = (
            weighted_score >= self.minimum_score and
            overall_confidence >= self.minimum_confidence and
            direction_consensus['agreement'] >= 0.67 and  # اتفاق 2 من 3 على الأقل
            filters_passed
        )
        
        return {
            'should_trade': should_trade,
            'direction': direction_consensus['direction'] if should_trade else 'neutral',
            'confidence': overall_confidence,
            'score': weighted_score,
            'details': {
                'technical': technical_result,
                'behavioral': behavioral_result,
                'ai': ai_result,
                'consensus': direction_consensus,
                'filters_passed': filters_passed
            }
        }
    
    def _determine_direction_consensus(self, tech: Dict, behav: Dict, ai: Dict) -> Dict:
        """تحديد الإجماع على الاتجاه"""
        directions = [tech['direction'], behav['direction'], ai['direction']]
        
        bullish_count = directions.count('bullish')
        bearish_count = directions.count('bearish')
        neutral_count = directions.count('neutral')
        
        total = len(directions)
        
        if bullish_count > bearish_count and bullish_count > neutral_count:
            return {
                'direction': 'bullish',
                'agreement': bullish_count / total,
                'votes': {'bullish': bullish_count, 'bearish': bearish_count, 'neutral': neutral_count}
            }
        elif bearish_count > bullish_count and bearish_count > neutral_count:
            return {
                'direction': 'bearish',
                'agreement': bearish_count / total,
                'votes': {'bullish': bullish_count, 'bearish': bearish_count, 'neutral': neutral_count}
            }
        else:
            return {
                'direction': 'neutral',
                'agreement': max(bullish_count, bearish_count, neutral_count) / total,
                'votes': {'bullish': bullish_count, 'bearish': bearish_count, 'neutral': neutral_count}
            }
```

## خصائص الاستراتيجية

### 1. المرونة والتكيف
- تتكيف مع ظروف السوق المختلفة
- تتعلم من النتائج السابقة
- قابلة للتخصيص حسب الأصل

### 2. الدقة العالية
- تحليل متعدد الطبقات
- فلترة متقدمة للإشارات الكاذبة
- نظام ثقة متطور

### 3. إدارة المخاطر
- تقييم المخاطر لكل إشارة
- حدود واضحة للدخول
- نظام إيقاف ذكي

### 4. السرعة والكفاءة
- معالجة سريعة للبيانات
- قرارات فورية
- تحسين مستمر للأداء

هذه الاستراتيجية ستوفر نظام تداول ذكي ومتقدم قادر على اتخاذ قرارات دقيقة ومربحة في السوق.
