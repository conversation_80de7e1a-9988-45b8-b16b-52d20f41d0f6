# استراتيجية الاختبار الشاملة

## نظرة عامة

استراتيجية الاختبار تهدف إلى ضمان جودة وموثوقية نظام التداول الذكي من خلال اختبارات متعددة المستويات تغطي جميع جوانب النظام.

## هرم الاختبار

```
                    ┌─────────────────┐
                    │  End-to-End     │
                    │   Tests (5%)    │
                    └─────────────────┘
                ┌─────────────────────────┐
                │   Integration Tests     │
                │        (15%)            │
                └─────────────────────────┘
        ┌─────────────────────────────────────┐
        │         Unit Tests                  │
        │           (80%)                     │
        └─────────────────────────────────────┘
```

## 1. اختبارات الوحدة (Unit Tests)

### 1.1 اختبار المؤشرات الفنية
```python
# tests/unit/test_technical_indicators.py
import pytest
import numpy as np
from strategy.indicators.technical_indicators import TechnicalIndicators

class TestTechnicalIndicators:
    def setup_method(self):
        self.indicators = TechnicalIndicators()
        self.sample_prices = [100, 102, 101, 103, 105, 104, 106, 108, 107, 109]
    
    def test_sma_calculation(self):
        """اختبار حساب المتوسط المتحرك البسيط"""
        period = 5
        sma = self.indicators.calculate_sma(self.sample_prices, period)
        
        # التحقق من الطول الصحيح
        expected_length = len(self.sample_prices) - period + 1
        assert len(sma) == expected_length
        
        # التحقق من القيمة الأولى
        expected_first = sum(self.sample_prices[:period]) / period
        assert abs(sma[0] - expected_first) < 0.001
    
    def test_rsi_calculation(self):
        """اختبار حساب مؤشر القوة النسبية"""
        rsi = self.indicators.calculate_rsi(self.sample_prices, 14)
        
        # التحقق من النطاق الصحيح (0-100)
        for value in rsi:
            assert 0 <= value <= 100
    
    def test_macd_calculation(self):
        """اختبار حساب MACD"""
        macd_data = self.indicators.calculate_macd(self.sample_prices, 12, 26, 9)
        
        # التحقق من وجود جميع المكونات
        assert 'macd' in macd_data
        assert 'signal' in macd_data
        assert 'histogram' in macd_data
        
        # التحقق من الأطوال المتساوية
        assert len(macd_data['macd']) == len(macd_data['signal'])
```

### 1.2 اختبار إدارة البيانات
```python
# tests/unit/test_data_management.py
import pytest
from unittest.mock import Mock, patch
from data.managers.historical_data_manager import HistoricalDataManager

class TestHistoricalDataManager:
    def setup_method(self):
        self.data_manager = HistoricalDataManager()
    
    @pytest.mark.asyncio
    async def test_gap_detection(self):
        """اختبار كشف الفجوات في البيانات"""
        # بيانات تحتوي على فجوة
        sample_data = [
            {'timestamp': 1000, 'open': 100, 'close': 101},
            {'timestamp': 1060, 'open': 101, 'close': 102},
            {'timestamp': 1180, 'open': 102, 'close': 103},  # فجوة 60 ثانية
            {'timestamp': 1240, 'open': 103, 'close': 104}
        ]
        
        gaps = self.data_manager.detect_gaps(sample_data, timeframe='1m')
        
        # يجب أن تكتشف فجوة واحدة
        assert len(gaps) == 1
        assert gaps[0]['start'] == 1060
        assert gaps[0]['end'] == 1180
    
    @pytest.mark.asyncio
    async def test_data_validation(self):
        """اختبار التحقق من صحة البيانات"""
        # بيانات صحيحة
        valid_data = {
            'timestamp': 1000,
            'open': 100.5,
            'high': 101.0,
            'low': 100.0,
            'close': 100.8
        }
        
        assert self.data_manager.validate_candle(valid_data) == True
        
        # بيانات خاطئة (high < low)
        invalid_data = {
            'timestamp': 1000,
            'open': 100.5,
            'high': 99.0,  # خطأ: high أقل من low
            'low': 100.0,
            'close': 100.8
        }
        
        assert self.data_manager.validate_candle(invalid_data) == False
```

### 1.3 اختبار طبقات التحليل
```python
# tests/unit/test_analysis_layers.py
import pytest
from strategy.analysis.technical_analysis import TechnicalAnalysisLayer
from strategy.analysis.behavioral_analysis import BehavioralAnalysisLayer

class TestAnalysisLayers:
    def setup_method(self):
        self.technical_layer = TechnicalAnalysisLayer()
        self.behavioral_layer = BehavioralAnalysisLayer()
    
    @pytest.mark.asyncio
    async def test_technical_analysis_output(self):
        """اختبار مخرجات التحليل الفني"""
        sample_candles = self.generate_sample_candles()
        
        result = await self.technical_layer.analyze(sample_candles)
        
        # التحقق من وجود المفاتيح المطلوبة
        assert 'score' in result
        assert 'direction' in result
        assert 'confidence' in result
        assert 'details' in result
        
        # التحقق من النطاقات الصحيحة
        assert 0 <= result['score'] <= 100
        assert 0 <= result['confidence'] <= 1
        assert result['direction'] in ['bullish', 'bearish', 'neutral']
    
    def test_pattern_detection(self):
        """اختبار كشف الأنماط"""
        # شمعة Pin Bar صعودية
        pin_bar_candle = {
            'open': 100.5,
            'high': 101.0,
            'low': 99.0,  # ذيل طويل
            'close': 100.8
        }
        
        pattern = self.behavioral_layer.detect_pin_bar(pin_bar_candle)
        
        assert pattern['detected'] == True
        assert pattern['direction'] == 'bullish'
        assert pattern['strength'] > 0.5
```

## 2. اختبارات التكامل (Integration Tests)

### 2.1 اختبار تكامل البيانات والتحليل
```python
# tests/integration/test_data_analysis_integration.py
import pytest
from data.managers.data_management_system import DataManagementSystem
from strategy.analysis.technical_analysis import TechnicalAnalysisLayer

class TestDataAnalysisIntegration:
    def setup_method(self):
        self.data_system = DataManagementSystem()
        self.analysis_layer = TechnicalAnalysisLayer()
    
    @pytest.mark.asyncio
    async def test_end_to_end_analysis(self):
        """اختبار التكامل من البيانات إلى التحليل"""
        asset = "EURUSD"
        timeframe = "5m"
        
        # جلب البيانات
        candles = await self.data_system.get_candles(asset, timeframe, count=100)
        
        # التحقق من جودة البيانات
        assert len(candles) == 100
        assert all('timestamp' in candle for candle in candles)
        
        # تشغيل التحليل
        analysis_result = await self.analysis_layer.analyze(candles)
        
        # التحقق من النتائج
        assert analysis_result is not None
        assert 'score' in analysis_result
```

### 2.2 اختبار تكامل الاستراتيجية
```python
# tests/integration/test_strategy_integration.py
import pytest
from strategy.decision.decision_engine import DecisionFusionEngine

class TestStrategyIntegration:
    def setup_method(self):
        self.decision_engine = DecisionFusionEngine()
    
    @pytest.mark.asyncio
    async def test_complete_decision_process(self):
        """اختبار العملية الكاملة لاتخاذ القرار"""
        asset = "GBPUSD"
        timeframe = "5m"
        
        # تشغيل العملية الكاملة
        decision = await self.decision_engine.make_trading_decision(asset, timeframe)
        
        # التحقق من المخرجات
        assert 'should_trade' in decision
        assert 'direction' in decision
        assert 'confidence' in decision
        assert 'score' in decision
        
        # التحقق من المنطق
        if decision['should_trade']:
            assert decision['confidence'] >= 0.8
            assert decision['score'] >= 80
```

## 3. اختبارات الأداء (Performance Tests)

### 3.1 اختبار سرعة المعالجة
```python
# tests/performance/test_processing_speed.py
import pytest
import time
from strategy.indicators.technical_indicators import TechnicalIndicators

class TestProcessingSpeed:
    def setup_method(self):
        self.indicators = TechnicalIndicators()
        # إنشاء بيانات كبيرة للاختبار
        self.large_dataset = [100 + i * 0.1 for i in range(10000)]
    
    def test_indicator_calculation_speed(self):
        """اختبار سرعة حساب المؤشرات"""
        start_time = time.time()
        
        # حساب عدة مؤشرات
        rsi = self.indicators.calculate_rsi(self.large_dataset, 14)
        sma = self.indicators.calculate_sma(self.large_dataset, 20)
        ema = self.indicators.calculate_ema(self.large_dataset, 20)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # يجب أن يكتمل في أقل من ثانية واحدة
        assert processing_time < 1.0
        
        # التحقق من صحة النتائج
        assert len(rsi) > 0
        assert len(sma) > 0
        assert len(ema) > 0
```

### 3.2 اختبار استهلاك الذاكرة
```python
# tests/performance/test_memory_usage.py
import pytest
import psutil
import os
from data.managers.data_management_system import DataManagementSystem

class TestMemoryUsage:
    def test_memory_efficiency(self):
        """اختبار كفاءة استهلاك الذاكرة"""
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # تحميل كمية كبيرة من البيانات
        data_system = DataManagementSystem()
        
        # محاكاة تحميل بيانات متعددة الأصول
        for i in range(10):
            large_data = [{'timestamp': j, 'open': 100, 'close': 101} 
                         for j in range(10000)]
            data_system.cache_data(f"ASSET_{i}", large_data)
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # يجب ألا يتجاوز الاستهلاك 500 MB
        assert memory_increase < 500
```

## 4. اختبارات النظام الكامل (End-to-End Tests)

### 4.1 اختبار سيناريو التداول الكامل
```python
# tests/e2e/test_complete_trading_scenario.py
import pytest
from unittest.mock import Mock, patch
from trading.managers.automated_trading_engine import AutomatedTradingEngine

class TestCompleteTradingScenario:
    def setup_method(self):
        self.trading_engine = AutomatedTradingEngine()
    
    @pytest.mark.asyncio
    async def test_automated_trading_session(self):
        """اختبار جلسة تداول آلي كاملة"""
        # إعداد جلسة تداول وهمية
        session_config = {
            'asset': 'EURUSD',
            'trade_amount': 10,
            'max_trades': 5,
            'risk_level': 'medium'
        }
        
        # محاكاة بيانات السوق
        with patch('data.managers.live_data_manager.LiveDataManager.get_live_data') as mock_data:
            mock_data.return_value = self.generate_mock_market_data()
            
            # تشغيل الجلسة
            session_result = await self.trading_engine.run_session(session_config)
            
            # التحقق من النتائج
            assert session_result['completed'] == True
            assert 'trades_executed' in session_result
            assert 'final_balance' in session_result
            assert 'performance_metrics' in session_result
```

## 5. اختبارات الأمان (Security Tests)

### 5.1 اختبار حماية البيانات الحساسة
```python
# tests/security/test_data_security.py
import pytest
from infrastructure.security.security_manager import SecurityManager

class TestDataSecurity:
    def setup_method(self):
        self.security_manager = SecurityManager()
    
    def test_credential_encryption(self):
        """اختبار تشفير بيانات الاعتماد"""
        original_credentials = {
            'email': '<EMAIL>',
            'password': 'secret_password'
        }
        
        # تشفير البيانات
        encrypted = self.security_manager.encrypt_credentials(original_credentials)
        
        # التحقق من التشفير
        assert encrypted != original_credentials
        assert 'email' not in str(encrypted)
        assert 'password' not in str(encrypted)
        
        # فك التشفير والتحقق
        decrypted = self.security_manager.decrypt_credentials(encrypted)
        assert decrypted == original_credentials
```

## 6. اختبارات التراجع (Regression Tests)

### 6.1 اختبار استقرار النتائج
```python
# tests/regression/test_result_stability.py
import pytest
from strategy.analysis.technical_analysis import TechnicalAnalysisLayer

class TestResultStability:
    def setup_method(self):
        self.analysis_layer = TechnicalAnalysisLayer()
        self.reference_data = self.load_reference_data()
    
    @pytest.mark.asyncio
    async def test_consistent_analysis_results(self):
        """اختبار ثبات نتائج التحليل"""
        # تشغيل التحليل عدة مرات على نفس البيانات
        results = []
        for _ in range(5):
            result = await self.analysis_layer.analyze(self.reference_data)
            results.append(result)
        
        # التحقق من ثبات النتائج
        first_result = results[0]
        for result in results[1:]:
            assert abs(result['score'] - first_result['score']) < 0.1
            assert result['direction'] == first_result['direction']
```

## 7. اختبارات الحمولة (Load Tests)

### 7.1 اختبار الأداء تحت الضغط
```python
# tests/load/test_system_load.py
import pytest
import asyncio
from concurrent.futures import ThreadPoolExecutor

class TestSystemLoad:
    @pytest.mark.asyncio
    async def test_concurrent_analysis(self):
        """اختبار التحليل المتزامن لعدة أصول"""
        assets = ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCAD']
        
        # تشغيل تحليل متزامن
        tasks = []
        for asset in assets:
            task = self.run_analysis_for_asset(asset)
            tasks.append(task)
        
        # انتظار اكتمال جميع المهام
        results = await asyncio.gather(*tasks)
        
        # التحقق من نجاح جميع التحليلات
        assert len(results) == len(assets)
        assert all(result is not None for result in results)
```

## 8. معايير النجاح

### 8.1 معايير الأداء
- **سرعة المعالجة**: < 1 ثانية لتحليل شمعة واحدة
- **استهلاك الذاكرة**: < 500 MB للنظام الكامل
- **دقة المؤشرات**: 99.9% مطابقة للمعايير المرجعية

### 8.2 معايير الموثوقية
- **معدل نجاح الاختبارات**: > 95%
- **تغطية الكود**: > 80%
- **عدد الأخطاء**: 0 أخطاء حرجة

### 8.3 معايير الأمان
- **تشفير البيانات**: 100% للبيانات الحساسة
- **حماية من الثغرات**: 0 ثغرات أمنية معروفة

## 9. أدوات الاختبار

### 9.1 أدوات الاختبار الأساسية
```bash
# pytest للاختبارات العامة
pip install pytest pytest-asyncio pytest-cov

# أدوات اختبار الأداء
pip install pytest-benchmark memory-profiler

# أدوات محاكاة البيانات
pip install pytest-mock responses
```

### 9.2 تشغيل الاختبارات
```bash
# تشغيل جميع الاختبارات
pytest tests/ -v

# اختبارات مع تقرير التغطية
pytest tests/ --cov=smart_trading_system --cov-report=html

# اختبارات الأداء فقط
pytest tests/performance/ -v --benchmark-only

# اختبارات التكامل
pytest tests/integration/ -v
```

هذه الاستراتيجية الشاملة للاختبار ستضمن جودة وموثوقية النظام قبل النشر في بيئة الإنتاج.
